import type React from "react"
import type { Metada<PERSON> } from "next/types"
import { Inter } from "next/font/google"
import "./globals.css"
import { headers } from "next/headers"
import { ThemeProvider } from "@/components/theme-provider"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { Toaster } from "@/components/ui/toaster"
import { AuthProvider } from "@/contexts/auth-context"
import StagewiseToolbar from "@/components/stagewise-toolbar"
import ServiceWorkerRegistration from "./sw-register"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "NIBOG - Baby Events Platform",
  description: "Discover and book baby-focused games and events across India",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#0072f5" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="NIBOG" />
        <link rel="apple-touch-icon" href="/logo192.png" />
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <AuthProvider>
            {children}
            <Toaster />
            <StagewiseToolbar />
            {/* Register service worker for offline support */}
            {process.env.NODE_ENV === 'production' && <ServiceWorkerRegistration />}
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}